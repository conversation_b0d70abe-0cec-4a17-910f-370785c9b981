name: hiv_predictor_app
description: "An AI-powered mobile app for HIV education and risk prediction."
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1

  # UI and core features
  cupertino_icons: ^1.0.2
  http: ^1.1.0
  shared_preferences: ^2.2.2

  # Media & launch
  url_launcher: ^6.2.2
  video_player: ^2.5.0

  # Maps & location
  google_maps_flutter: ^2.5.0
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  geolocator: ^10.1.0

  # Notifications
  flutter_local_notifications: ^16.3.0
  timezone: ^0.9.2

  # Additional dependencies
  permission_handler: ^11.1.0
  firebase_core: ^2.32.0
  firebase_database: ^10.3.4

  # Authentication & State Management
  crypto: ^3.0.3
  firebase_auth: ^4.16.0
  cloud_firestore: ^4.17.5
  firebase_analytics: ^10.10.7
  firebase_messaging: ^14.7.10
  logger: ^2.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/fonts/
    - assets/videos/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Bold.ttf
