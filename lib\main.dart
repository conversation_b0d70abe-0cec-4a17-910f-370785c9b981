import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import 'screens/main_navigation_screen.dart';
import 'services/service_factory.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  final logger = Logger();

  // Skip Firebase initialization on web to avoid compilation issues
  if (!kIsWeb) {
    // Firebase will be initialized by the service factory when needed
    logger.i('Running on mobile platform - Firebase services available');
  } else {
    logger.i('Running on web platform - using web-safe services');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AuthServiceWrapper>(create: (_) => AuthServiceWrapper()),
        Provider<DatabaseServiceWrapper>(
          create: (_) => DatabaseServiceWrapper(),
        ),
      ],
      child: <PERSON><PERSON>pp(
        title: 'HIV Predictor App',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF1976D2)),
          fontFamily: 'Poppins',
        ),
        home: const MainNavigationScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
